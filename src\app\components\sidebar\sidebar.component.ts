import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent {
  @Output() platformSelected = new EventEmitter<'whatsapp' | 'instagram'>();
  
  selectedPlatform: 'whatsapp' | 'instagram' = 'whatsapp';

  constructor(private router: Router) {}

  selectPlatform(platform: 'whatsapp' | 'instagram') {
    this.selectedPlatform = platform;
    this.platformSelected.emit(platform);
    this.router.navigate([`/${platform}`]);
  }
}
