import { Component } from '@angular/core';
import { RouterOutlet, Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent {
  title = 'social-media-tool-ui';
  selectedPlatform: 'whatsapp' | 'instagram' | null = null;

  constructor(private router: Router) {}

  selectPlatform(platform: 'whatsapp' | 'instagram') {
    this.selectedPlatform = platform;
    this.router.navigate([`/${platform}`]);
  }
}
